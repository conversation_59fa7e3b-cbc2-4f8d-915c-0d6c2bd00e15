<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fs.swap.system.mapper.CommunityHelpMapper">

    <resultMap type="CommunityHelp" id="CommunityHelpResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="images"    column="images"    />
        <result property="category"    column="category"    />
        <result property="publishType"    column="publish_type"    />
        <result property="contactInfo"    column="contact_info"    />
        <result property="viewCount"    column="view_count"    />
        <result property="status"    column="status"    />
        <result property="auditRemark"    column="audit_remark"    />
        <result property="communityId"    column="community_id"    />
        <result property="residentialId"    column="residential_id"    />
        <result property="userId"    column="user_id"    />
        <result property="buyerId"    column="buyer_id"    />
        <result property="endTime"    column="end_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="deleted"    column="deleted"    />
    </resultMap>

    <sql id="selectCommunityHelpVo">
        select id, title, content, images, category, publish_type, contact_info, view_count, status, audit_remark, community_id, residential_id, user_id, buyer_id, end_time, create_time, update_time, deleted from community_help
    </sql>

    <select id="selectCommunityHelpList" parameterType="CommunityHelp" resultMap="CommunityHelpResult">
        <include refid="selectCommunityHelpVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="content != null  and content != ''"> and content like concat('%', #{content}, '%')</if>
            <if test="searchValue != null and searchValue != ''"> and (title like concat('%', #{searchValue}, '%') or content like concat('%', #{searchValue}, '%'))</if>
            <if test="category != null  and category != ''"> and category = #{category}</if>
            <if test="publishType != null  and publishType != ''"> and publish_type = #{publishType}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="communityId != null "> and community_id = #{communityId}</if>
            <if test="residentialId != null "> and residential_id = #{residentialId}</if>
            and deleted = 0
        </where>
        order by create_time desc
    </select>
    
    <select id="selectCommunityHelpById" parameterType="Long" resultMap="CommunityHelpResult">
        <include refid="selectCommunityHelpVo"/>
        where id = #{id} and deleted = 0
    </select>
        
    <insert id="insertCommunityHelp" parameterType="CommunityHelp" useGeneratedKeys="true" keyProperty="id">
        insert into community_help
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="images != null">images,</if>
            <if test="category != null and category != ''">category,</if>
            <if test="publishType != null and publishType != ''">publish_type,</if>
            <if test="contactInfo != null and contactInfo != ''">contact_info,</if>
            <if test="viewCount != null">view_count,</if>
            <if test="status != null">status,</if>
            <if test="auditRemark != null">audit_remark,</if>
            <if test="communityId != null">community_id,</if>
            <if test="residentialId != null">residential_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="buyerId != null">buyer_id,</if>
            <if test="endTime != null">end_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="deleted != null">deleted,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="images != null">#{images},</if>
            <if test="category != null and category != ''">#{category},</if>
            <if test="publishType != null and publishType != ''">#{publishType},</if>
            <if test="contactInfo != null and contactInfo != ''">#{contactInfo},</if>
            <if test="viewCount != null">#{viewCount},</if>
            <if test="status != null">#{status},</if>
            <if test="auditRemark != null">#{auditRemark},</if>
            <if test="communityId != null">#{communityId},</if>
            <if test="residentialId != null">#{residentialId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="buyerId != null">#{buyerId},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="deleted != null">#{deleted},</if>
         </trim>
    </insert>

    <update id="updateCommunityHelp" parameterType="CommunityHelp">
        update community_help
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="images != null">images = #{images},</if>
            <if test="category != null and category != ''">category = #{category},</if>
            <if test="publishType != null and publishType != ''">publish_type = #{publishType},</if>
            <if test="contactInfo != null and contactInfo != ''">contact_info = #{contactInfo},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
            <if test="status != null">status = #{status},</if>
            <if test="auditRemark != null">audit_remark = #{auditRemark},</if>
            <if test="communityId != null">community_id = #{communityId},</if>
            <if test="residentialId != null">residential_id = #{residentialId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="buyerId != null">buyer_id = #{buyerId},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="deleted != null">deleted = #{deleted},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCommunityHelpById" parameterType="Long">
        update community_help set deleted = 1 where id = #{id}
    </delete>

    <delete id="deleteCommunityHelpByIds" parameterType="String">
        update community_help set deleted = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="incrementViewCount" parameterType="Long">
        update community_help set view_count = IFNULL(view_count, 0) + 1 where id = #{id}
    </update>

    <select id="getCommunityHelpStatsByResidential" parameterType="Long" resultType="java.util.Map">
        select
            publish_type as publishType,
            count(*) as count
        from community_help
        where residential_id = #{residentialId}
            and status = '1'
            and deleted = 0
        group by publish_type
    </select>
</mapper>
